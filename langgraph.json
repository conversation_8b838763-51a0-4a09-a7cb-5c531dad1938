{"dependencies": ["."], "graphs": {"agent": "./src/agent/graph.py:graph"}, "env": ".env", "image_distro": "wolfi", "input_schema": {"type": "object", "properties": {"messages": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["human", "ai", "system"]}, "content": {"type": "string"}}, "required": ["type", "content"]}, "description": "Array of messages in the conversation", "default": [{"type": "human", "content": "Cerco per i prossimi giorni, un aperitivo"}]}, "user_id": {"type": "string", "description": "Unique identifier for the user", "default": "fe95e629-0a4e-474b-97d1-fafe9d6863e3"}, "email_address": {"type": "string", "format": "email", "description": "User's email address", "default": "<EMAIL>"}, "latitude": {"type": "string", "description": "User's latitude position", "default": "45.4666"}, "longitude": {"type": "string", "description": "User's longitude position", "default": "9.1832"}, "session_id": {"type": "string", "description": "Memory session identifier", "default": "1233"}, "memory_lenght": {"type": "string", "description": "Memory length parameter", "default": "15"}}, "required": ["messages"]}}