# Memory Functionality Implementation

## Overview

The LangGraph workflow implements a **hybrid memory management system** that provides both automatic and configurable memory limiting functionality. This system controls how many conversation messages are maintained and sent to the LLM, preventing token limit issues and improving performance by maintaining only the most relevant recent conversation history.

## Architecture

The memory management system uses a **two-tier approach**:

1. **Memory-Aware Reducer**: Provides baseline memory management at the state level
2. **Dynamic Memory Limiting**: Allows per-conversation memory configuration in `call_model`

## Implementation Details

### 1. Memory-Aware Reducer

The baseline memory management is implemented in `src/agent/state.py` with the `memory_aware_reducer()` function:

```python
def memory_aware_reducer(existing: list[AnyMessage], new: list[AnyMessage]) -> list[AnyMessage]:
    """High-performance reducer that maintains memory limit during updates.

    Note: This reducer uses a default memory limit of 15 messages.
    For dynamic memory limits, use the limit_message_history function in call_model.py
    """
    # Fast path optimizations
    if not existing:
        return new
    if not new:
        return existing

    # Default memory length of 15
    memory_length = 15

    # Combine and limit messages efficiently
    combined = existing + new
    if len(combined) <= memory_length:
        return combined

    return combined[-memory_length:]
```

This reducer is automatically applied to the `messages` field in the state:

```python
class State(TypedDict):
    """Chatbot state with message history and user context."""
    messages: Annotated[list[AnyMessage], memory_aware_reducer]
    # ... other fields
```

### 2. Dynamic Memory Limiting

The configurable memory limiting is implemented in `src/agent/nodes/call_model.py` with the `limit_message_history()` function:

```python
def limit_message_history(messages: List[BaseMessage], memory_length: int) -> List[BaseMessage]:
    """Limit message history to the specified number of recent messages.
    
    Args:
        messages: List of conversation messages
        memory_length: Maximum number of messages to keep
        
    Returns:
        List of messages limited to the specified length
    """
    if memory_length <= 0:
        return []
    
    # Keep only the most recent messages up to the memory limit
    return messages[-memory_length:]
```

### 3. Integration with LangGraph

The hybrid memory system works as follows:

1. **State Updates**: When new messages are added to the state, the `memory_aware_reducer` automatically limits them to 15 messages
2. **Call Model Processing**: The `call_model` function can apply additional memory limiting based on the `memory_lenght` parameter
3. **Final Limiting**: The most restrictive limit is applied (either reducer default or user-specified)

```python
async def call_model(state: State, config: RunnableConfig) -> Dict[str, Any]:
    # ... setup code ...

    # Apply memory length limiting (overrides reducer default if more restrictive)
    conversation_messages = state["messages"]  # Already limited by reducer to 15
    memory_length_str = state.get("memory_lenght", "15")

    try:
        memory_length = int(memory_length_str)
    except (ValueError, TypeError):
        memory_length = 15

    # Apply additional limiting if user wants fewer messages
    limited_messages = limit_message_history(conversation_messages, memory_length)

    # Prepare messages with system prompt and limited conversation history
    messages = [SystemMessage(content=system_prompt)] + limited_messages
```

### 4. Hybrid Approach Benefits

- **Automatic Protection**: Reducer prevents unbounded memory growth
- **Performance**: Fast path optimizations in the reducer
- **Flexibility**: Per-conversation memory limits via `memory_lenght`
- **Efficiency**: No unnecessary processing when limits aren't exceeded

## Configuration

### Default Settings

- **Default memory length**: 15 messages
- **Parameter name**: `memory_lenght` (note the spelling in the original schema)
- **Type**: String (converted to integer internally)

### Configuration Files

The memory length is configured in several places:

1. **`studio_defaults.json`**: Default value "15"
2. **`langgraph.json`**: Schema definition with default "15"
3. **`studio_test_input.json`**: Test input with "15"

### Runtime Configuration

You can set the memory length when invoking the graph:

```python
inputs = {
    "messages": [HumanMessage(content="Hello")],
    "memory_lenght": "10",  # Limit to 10 messages
    "user_id": "user123",
    # ... other parameters
}
```

## Behavior

### Message Selection

- **Most Recent**: Always keeps the most recent messages
- **Order Preserved**: Message order is maintained
- **Inclusive Counting**: Both human and AI messages count toward the limit

### Edge Cases

- **Zero/Negative**: Returns empty list (no conversation history)
- **Invalid Values**: Defaults to 15 messages
- **Larger than Available**: Returns all available messages
- **Empty Conversation**: Returns empty list

### Examples

```python
# Original conversation: 10 messages
# memory_lenght: "3"
# Result: Last 3 messages sent to LLM

# Original conversation: 5 messages  
# memory_lenght: "10"
# Result: All 5 messages sent to LLM

# Original conversation: 8 messages
# memory_lenght: "0" 
# Result: No conversation history, only system prompt
```

## Testing

### Unit Tests

Comprehensive unit tests are available in `tests/unit_tests/test_memory_functionality.py`:

- Basic limiting functionality
- Edge cases (zero, negative, empty)
- Order preservation
- Most recent message selection
- Parameter parsing scenarios

### Running Tests

```bash
# Run memory functionality tests
python -m pytest tests/unit_tests/test_memory_functionality.py -v

# Run all unit tests
python -m pytest tests/unit_tests/ -v
```

## Benefits

1. **Token Management**: Prevents exceeding LLM token limits
2. **Performance**: Reduces processing time for long conversations
3. **Cost Optimization**: Fewer tokens = lower API costs
4. **Relevance**: Maintains focus on recent conversation context
5. **Configurable**: Adjustable per conversation or globally

## Migration Notes

### Backward Compatibility

- Existing conversations continue to work
- Default behavior maintains 15 messages (previous effective behavior)
- No breaking changes to existing API

### Upgrading

No migration steps required. The functionality is:
- **Automatic**: Works immediately with existing configurations
- **Safe**: Defaults to reasonable values for invalid inputs
- **Transparent**: Doesn't change external API

## Troubleshooting

### Common Issues

1. **Invalid memory_lenght**: Automatically defaults to 15
2. **Zero messages**: Check if memory_lenght is set to 0 or negative
3. **All history lost**: Verify memory_lenght is not too small

### Debugging

```python
# Check current memory setting
print(f"Memory length: {state.get('memory_lenght', 'not set')}")

# Check message counts
print(f"Original: {len(state['messages'])} messages")
limited = limit_message_history(state["messages"], memory_length)
print(f"Limited: {len(limited)} messages")
```

## Future Enhancements

Potential improvements for future versions:

1. **Smart Truncation**: Preserve important messages (e.g., system instructions)
2. **Token-Based Limiting**: Limit by token count instead of message count
3. **Sliding Window**: More sophisticated conversation windowing
4. **Context Preservation**: Maintain conversation summaries for longer context
5. **Dynamic Adjustment**: Auto-adjust based on conversation complexity

## Related Files

- `src/agent/nodes/call_model.py`: Core implementation
- `src/agent/state.py`: State definition with memory_lenght field
- `tests/unit_tests/test_memory_functionality.py`: Unit tests
- `studio_defaults.json`: Default configuration
- `langgraph.json`: Schema definition
