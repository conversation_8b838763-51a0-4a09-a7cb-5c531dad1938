[project]
name = "agent"
version = "0.0.1"
description = "Starter template for making a new agent LangGraph."
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.10"
dependencies = [
     "fastapi>=0.116.1",
     "langchain>=0.3.26",
     "langchain-community>=0.3.27",
     "langchain-openai>=0.3.28",
     "langgraph>=0.5.2",
     "python-dotenv>=1.0.1",
     "langchain-mcp-adapters>=0.1.9",
]


[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]


[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    # We actually do want to import from typing_extensions
    "UP035",
    # Relax the convention by _not_ requiring documentation for every function parameter.
    "D417",
    "E501",
]
[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]
[tool.ruff.lint.pydocstyle]
convention = "google"

[dependency-groups]
dev = [
    "anyio>=4.7.0",
    "langgraph-cli[inmem]>=0.2.8",
    "mypy>=1.13.0",
    "pytest>=8.3.5",
    "ruff>=0.8.2",
]


