
from langgraph.graph.message import MessagesState
from typing import Optional

class State(MessagesState):
    """Chatbot state with message history and user context."""
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    question: Optional[str] = None  # User query/question
    user_id: Optional[str] = None
    email_address: Optional[str] = None
    session_id: Optional[str] = None
    memory_lenght: Optional[str] = None
