
from langgraph.graph.message import add_messages, AnyMessage
from typing import Optional, Annotated, TypedDict, Any
from langchain_core.messages import BaseMessage


def memory_aware_reducer(existing: list[AnyMessage], new: list[AnyMessage], config=None) -> list[AnyMessage]:
    """High-performance reducer that maintains memory limit during updates."""
    # Fast path: if no existing messages, just return new ones
    if not existing:
        return new
    
    # Fast path: if no new messages, return existing
    if not new:
        return existing
    
    # Get memory length from config (default to 15)
    memory_length = 15
    if config and hasattr(config, 'configurable'):
        try:
            memory_length = int(config.configurable.get('memory_lenght', '15'))
        except (ValueError, TypeError):
            memory_length = 15
    
    # Early exit if memory_length is 0
    if memory_length <= 0:
        return []
    
    # Combine messages efficiently
    combined = existing + new
    
    # Only slice if we exceed the limit (avoid unnecessary operations)
    if len(combined) <= memory_length:
        return combined
    
    # Return only the most recent messages
    return combined[-memory_length:]


class State(TypedDict):
    """Chatbot state with message history and user context."""
    messages: Annotated[list[AnyMessage], memory_aware_reducer]
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    question: Optional[str] = None
    user_id: Optional[str] = None
    email_address: Optional[str] = None
    session_id: Optional[str] = None
    memory_lenght: Optional[str] = None
