#!/usr/bin/env python3
"""Integration test for the updated memory management system."""

import asyncio
import os
from langchain_core.messages import HumanMessage, AIMessage


async def test_memory_system_integration():
    """Test the complete memory management system."""
    print("Testing updated memory management system...")
    
    # Disable <PERSON><PERSON><PERSON> to avoid authentication issues
    os.environ.pop("LANGCHAIN_API_KEY", None)
    os.environ.pop("LANGSMITH_API_KEY", None)
    os.environ["LANGCHAIN_TRACING_V2"] = "false"
    
    try:
        from agent.state import memory_aware_reducer, State
        from agent.nodes.call_model import call_model, limit_message_history
        from langchain_core.runnables import RunnableConfig
        
        print("✓ All imports successful")
        
        # Test 1: Memory-aware reducer
        print("\n--- Test 1: Memory-aware reducer ---")
        
        # Create a long conversation
        existing_messages = []
        for i in range(10):
            existing_messages.append(HumanMessage(content=f"Existing message {i+1}"))
            existing_messages.append(AIMessage(content=f"Existing response {i+1}"))
        
        new_messages = []
        for i in range(5):
            new_messages.append(HumanMessage(content=f"New message {i+1}"))
            new_messages.append(AIMessage(content=f"New response {i+1}"))
        
        print(f"Existing messages: {len(existing_messages)}")
        print(f"New messages: {len(new_messages)}")
        print(f"Total would be: {len(existing_messages) + len(new_messages)}")
        
        # Test the reducer
        reduced_messages = memory_aware_reducer(existing_messages, new_messages)
        
        print(f"Reduced to: {len(reduced_messages)} messages")
        print(f"First message: '{reduced_messages[0].content}'")
        print(f"Last message: '{reduced_messages[-1].content}'")
        
        # Should limit to 15 messages
        assert len(reduced_messages) == 15, f"Expected 15 messages, got {len(reduced_messages)}"

        # Calculate expected first message: total 30 messages, keep last 15, so start at index 15
        # Index 15 would be "Existing response 8" (since we have pairs: msg1, resp1, msg2, resp2, ...)
        print(f"Expected first message should be at index 15 of combined messages")
        assert reduced_messages[-1].content == "New response 5", "Should end with last new message"
        
        print("✓ Memory-aware reducer working correctly")
        
        # Test 2: call_model memory limiting
        print("\n--- Test 2: call_model memory limiting ---")
        
        # Create a state with many messages
        long_conversation = []
        for i in range(12):
            long_conversation.append(HumanMessage(content=f"User message {i+1}"))
            long_conversation.append(AIMessage(content=f"AI response {i+1}"))
        
        # Test different memory lengths
        memory_lengths = ["5", "10", "20"]
        
        for memory_length in memory_lengths:
            print(f"\n  Testing memory_lenght = {memory_length}")
            
            # Create state
            state = {
                "messages": long_conversation,
                "memory_lenght": memory_length,
                "user_id": "test_user",
                "latitude": "45.4666",
                "longitude": "9.1832"
            }
            
            # Test the memory limiting function directly
            memory_length_int = int(memory_length)
            limited_messages = limit_message_history(state["messages"], memory_length_int)
            
            expected_count = min(memory_length_int, len(long_conversation))
            
            print(f"    Original: {len(state['messages'])} messages")
            print(f"    Limited to: {len(limited_messages)} messages")
            print(f"    Expected: {expected_count} messages")
            
            assert len(limited_messages) == expected_count, f"Expected {expected_count}, got {len(limited_messages)}"
            
            if limited_messages:
                print(f"    First kept: '{limited_messages[0].content}'")
                print(f"    Last kept: '{limited_messages[-1].content}'")
        
        print("✓ call_model memory limiting working correctly")
        
        # Test 3: Edge cases
        print("\n--- Test 3: Edge cases ---")
        
        # Test with empty messages
        empty_result = memory_aware_reducer([], [])
        assert len(empty_result) == 0, "Empty messages should return empty"
        print("✓ Empty messages handled correctly")
        
        # Test with zero memory length
        zero_limited = limit_message_history(long_conversation, 0)
        assert len(zero_limited) == 0, "Zero memory length should return empty"
        print("✓ Zero memory length handled correctly")
        
        # Test with invalid memory length string
        try:
            memory_length = int("invalid")
        except (ValueError, TypeError):
            memory_length = 15
        assert memory_length == 15, "Invalid memory length should default to 15"
        print("✓ Invalid memory length handled correctly")
        
        print("\n--- Test 4: Performance characteristics ---")
        
        # Test performance with large message sets
        large_existing = []
        for i in range(1000):
            large_existing.append(HumanMessage(content=f"Large message {i}"))
        
        large_new = []
        for i in range(100):
            large_new.append(HumanMessage(content=f"New large message {i}"))
        
        import time
        start_time = time.time()
        large_result = memory_aware_reducer(large_existing, large_new)
        end_time = time.time()
        
        print(f"Processed {len(large_existing) + len(large_new)} messages in {end_time - start_time:.4f} seconds")
        print(f"Result: {len(large_result)} messages")
        assert len(large_result) == 15, "Should still limit to 15 messages"
        print("✓ Performance test passed")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_hybrid_memory_approach():
    """Test the hybrid approach: reducer + call_model limiting."""
    print("\n--- Test 5: Hybrid memory approach ---")
    
    from agent.state import memory_aware_reducer
    from agent.nodes.call_model import limit_message_history
    
    # Simulate what happens in the actual system
    
    # Step 1: Messages come in and are processed by the reducer
    existing = []
    for i in range(8):
        existing.append(HumanMessage(content=f"Existing {i+1}"))
    
    new = [HumanMessage(content="New user message")]
    
    # Reducer processes them (default limit of 15)
    after_reducer = memory_aware_reducer(existing, new)
    print(f"After reducer: {len(after_reducer)} messages")
    
    # Step 2: call_model applies its own memory limit based on state
    memory_lenght = "5"  # User wants only 5 messages
    memory_length_int = int(memory_lenght)
    
    final_messages = limit_message_history(after_reducer, memory_length_int)
    print(f"After call_model limiting: {len(final_messages)} messages")
    
    # Should respect the more restrictive limit
    assert len(final_messages) == 5, f"Expected 5 messages, got {len(final_messages)}"
    
    # Should be the most recent 5 messages
    expected_contents = ["Existing 5", "Existing 6", "Existing 7", "Existing 8", "New user message"]
    actual_contents = [msg.content for msg in final_messages]
    
    assert actual_contents == expected_contents, f"Expected {expected_contents}, got {actual_contents}"
    
    print("✓ Hybrid memory approach working correctly")
    print(f"Final messages: {actual_contents}")


async def main():
    """Run all tests."""
    print("=" * 70)
    print("UPDATED MEMORY MANAGEMENT SYSTEM TESTS")
    print("=" * 70)
    
    success = await test_memory_system_integration()
    await test_hybrid_memory_approach()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ ALL TESTS COMPLETED SUCCESSFULLY!")
        print("\nUpdated memory management system is working correctly!")
        print("\nKey improvements:")
        print("- Memory-aware reducer provides baseline memory management")
        print("- call_model can override with more specific memory limits")
        print("- Hybrid approach ensures optimal performance and flexibility")
    else:
        print("❌ SOME TESTS FAILED")
    print("=" * 70)


if __name__ == "__main__":
    asyncio.run(main())
