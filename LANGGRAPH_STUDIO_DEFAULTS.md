# LangGraph Studio Default Values Configuration

This document explains the default values setup for testing the CatchUp customer service agent in LangGraph Studio.

## Overview

The CatchUp agent is a multi-tenant marketplace customer service system that requires specific input parameters for proper testing. This configuration provides realistic default values that simulate a typical user interaction.

## Files Structure

### 1. Input Configuration Files

| File | Purpose | Description |
|------|---------|-------------|
| `studio_test_input.json` | Test Input | Ready-to-use input data for LangGraph Studio |
| `studio_test_config.json` | Test Configuration | LLM and system configuration |
| `studio_defaults.json` | Schema Definition | Complete input/output schema with defaults |
| `langgraph.json` | Main Config | Updated with input schema for Studio integration |

## Default Values Explained

### User Context
```json
{
  "user_id": "fe95e629-0a4e-474b-97d1-fafe9d6863e3",
  "email_address": "<EMAIL>",
  "session_id": "1233",
  "memory_lenght": "15"
}
```

- **user_id**: UUID format identifier for user tracking
- **email_address**: Valid email for communication features
- **session_id**: Memory session identifier for conversation continuity
- **memory_lenght**: Memory length parameter (15 messages)

### Location Context
```json
{
  "latitude": "45.4666",
  "longitude": "9.1832"
}
```

- **Coordinates**: Milan, Italy (Duomo area)
- **Purpose**: Location-based deal and venue recommendations
- **Format**: String coordinates for API compatibility

### Message Content
```json
{
  "messages": [
    {
      "type": "human",
      "content": "Cerco per i prossimi giorni, un aperitivo"
    }
  ]
}
```

- **Language**: Italian (primary market)
- **Query Type**: Looking for aperitivo venues
- **Intent**: Location-based service request
- **Translation**: "Looking for an aperitivo for the next few days"

### LLM Configuration
```json
{
  "configurable": {
    "model_name": "anthropic/claude-3.5-sonnet",
    "system_prompt": "You are a helpful customer service assistant for a multi-tenant marketplace."
  }
}
```

- **Model**: Claude 3.5 Sonnet (high-quality reasoning)
- **System Prompt**: Marketplace customer service context

## How to Use in LangGraph Studio

### Step 1: Start LangGraph Studio
```bash
cd /path/to/your/project
langgraph dev
```

### Step 2: Load Default Input
1. Open LangGraph Studio in your browser
2. Navigate to the **Input** tab
3. Copy the contents of `studio_test_input.json`
4. Paste into the input field

### Step 3: Load Configuration
1. Navigate to the **Config** tab
2. Copy the contents of `studio_test_config.json`
3. Paste into the config field

### Step 4: Run Test
1. Click the **Run** button
2. Observe the agent's response
3. Check tool calls and reasoning

## Expected Behavior

### Agent Response Flow
1. **Input Processing**: Agent receives Italian query about aperitivo
2. **Location Analysis**: Uses Milan coordinates for local search
3. **Tool Activation**: Calls MCP tools for:
   - Category search (aperitivo/drinks)
   - Deal lookup in Milan area
   - Venue recommendations
4. **Response Generation**: Provides relevant suggestions in Italian/English

### Tool Integration
The agent should utilize these MCP tools:
- `get_categories` - Find drink/aperitivo categories
- `get_deals` - Search for relevant deals in Milan
- `get_bookings` - Check booking availability
- `send_email` - Send details if requested

## Customization Options

### Alternative Test Scenarios

#### Different Location (Rome)
```json
{
  "latitude": "41.9028",
  "longitude": "12.4964"
}
```

#### Different Query Types
```json
{
  "content": "Ho una prenotazione per stasera, posso modificarla?"
}
```
*Translation: "I have a booking for tonight, can I modify it?"*

#### English Query
```json
{
  "content": "I'm looking for restaurants near me with good deals"
}
```

### Configuration Variants

#### Different Model
```json
{
  "model_name": "openai/gpt-4-turbo"
}
```

#### Custom System Prompt
```json
{
  "system_prompt": "You are a specialized Italian hospitality assistant focused on food and drink recommendations."
}
```

## Troubleshooting

### Common Issues

1. **No Tool Calls**: Check MCP server is running
2. **Location Errors**: Verify coordinate format (string, not number)
3. **Memory Issues**: Ensure session_id is provided
4. **Language Mixing**: Agent should detect Italian and respond appropriately

### Validation Checklist

- [ ] Input follows exact JSON schema
- [ ] All required fields are present
- [ ] Coordinates are valid strings
- [ ] Message type is "human"
- [ ] Configuration includes model_name

## Schema Reference

### Complete Input Schema
```json
{
  "type": "object",
  "properties": {
    "messages": { "type": "array", "required": true },
    "user_id": { "type": "string" },
    "email_address": { "type": "string", "format": "email" },
    "latitude": { "type": "string" },
    "longitude": { "type": "string" },
    "session_id": { "type": "string" },
    "memory_lenght": { "type": "string" }
  },
  "required": ["messages"]
}
```

### State Fields
Based on `src/agent/state.py`:
- `messages`: Conversation history (MessagesState)
- `latitude/longitude`: User position
- `question`: User query (optional)
- `user_id`: User identifier
- `email_address`: Contact information
- `session_id`: Memory session
- `memory_lenght`: Memory configuration

## Integration Notes

- **MCP Tools**: Requires CatchUp MCP server running
- **Environment**: Uses `.env` file for API keys
- **Dependencies**: All dependencies in `pyproject.toml`
- **Testing**: Run `pytest tests/integration_tests/` for validation

This configuration provides a complete testing environment for the CatchUp customer service agent with realistic Italian marketplace scenarios.
